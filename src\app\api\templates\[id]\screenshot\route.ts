import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import { getTemplateById } from '@/lib/database';
import { readFile } from 'fs/promises';
import { join } from 'path';

function getLayoutCSS(layoutSize: string): string {
  const baseCSS = `
    <style>
      /* Outer container for page sizing - doesn't interfere with original content */
      .ldis-page-wrapper {
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        padding: 1in;
        box-sizing: border-box;
        position: relative;
        z-index: 0;
      }

      /* Inner container preserves original HTML positioning */
      .ldis-content-container {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: visible;
      }

      /* Screen-only styling */
      @media screen {
        body {
          margin: 0;
          padding: 20px;
          background: #f5f5f5;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
      }

      /* Print styling */
      @media print {
        body {
          margin: 0;
          padding: 0;
          background: white;
        }
        .ldis-page-wrapper {
          box-shadow: none;
          margin: 0;
        }
      }
    </style>
  `;

  // Layout-specific dimensions
  const layoutStyles = {
    'A4': `
      .ldis-page-wrapper {
        width: 8.27in;
        min-height: 11.69in;
      }
    `,
    'Letter': `
      .ldis-page-wrapper {
        width: 8.5in;
        min-height: 11in;
      }
    `,
    'Legal': `
      .ldis-page-wrapper {
        width: 8.5in;
        min-height: 14in;
      }
    `
  };

  const layoutCSS = layoutStyles[layoutSize as keyof typeof layoutStyles] || layoutStyles['A4'];
  
  return baseCSS + `<style>${layoutCSS}</style>`;
}

/**
 * GET /api/templates/[id]/screenshot - Generate and return template screenshot
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let browser;
  
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Build file path
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);
    const filePath = join(templateDir, template.filename);

    // Read HTML file
    let htmlContent = await readFile(filePath, 'utf-8');

    // Rewrite image paths to use absolute URLs
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const escapedTemplateName = template.template_name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const templateFilesPattern = new RegExp(
      `src\\s*=\\s*["'](?:\\.\\/)?${escapedTemplateName}_files\\/([^"']+)["']`,
      'gi'
    );
    htmlContent = htmlContent.replace(
      templateFilesPattern,
      `src="${baseUrl}/api/templates/${templateId}/images/$1"`
    );

    // Add layout CSS and wrap content
    const layoutCSS = getLayoutCSS(template.layout_size || 'A4');
    const wrappedHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${template.template_name}</title>
        ${layoutCSS}
      </head>
      <body>
        <div class="ldis-page-wrapper">
          <div class="ldis-content-container">
            ${htmlContent}
          </div>
        </div>
      </body>
      </html>
    `;

    // Launch Puppeteer
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Set viewport for consistent screenshots
    await page.setViewport({
      width: 1200,
      height: 1600,
      deviceScaleFactor: 2
    });

    // Load the HTML content
    await page.setContent(wrappedHtml, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Take screenshot of the template container
    const element = await page.$('.ldis-page-wrapper');
    if (!element) {
      throw new Error('Template container not found');
    }

    const screenshot = await element.screenshot({
      type: 'png',
      omitBackground: false
    });

    await browser.close();

    // Return the screenshot as PNG
    return new NextResponse(Buffer.from(screenshot), {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating template screenshot:', error);
    
    if (browser) {
      await browser.close();
    }
    
    return NextResponse.json(
      { error: 'Failed to generate template screenshot' },
      { status: 500 }
    );
  }
}
