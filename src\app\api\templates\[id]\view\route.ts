import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById } from '@/lib/database';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * Generate CSS for different layout sizes
 */
function getLayoutCSS(layoutSize: string): string {
  const baseCSS = `
    <style>
      @media print {
        @page {
          margin: 0.5in;
        }
        body {
          margin: 0;
          padding: 0;
        }
      }

      body {
        font-family: 'Times New Roman', serif;
        line-height: 1.2;
        margin: 0;
        padding: 20px;
        background: white;
      }

      .page-container {
        max-width: 100%;
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        min-height: 100vh;
        padding: 0;
      }
    `;

  switch (layoutSize.toUpperCase()) {
    case 'A4':
      return baseCSS + `
        .page-container {
          width: 210mm;
          min-height: 297mm;
        }

        @media screen {
          body {
            background: #f5f5f5;
            padding: 20px;
          }
          .page-container {
            margin: 20px auto;
            padding: 20mm;
          }
        }

        @media print {
          @page {
            size: A4;
            margin: 20mm;
          }
        }
      </style>`;

    case 'LETTER':
      return baseCSS + `
        .page-container {
          width: 8.5in;
          min-height: 11in;
        }

        @media screen {
          body {
            background: #f5f5f5;
            padding: 20px;
          }
          .page-container {
            margin: 20px auto;
            padding: 1in;
          }
        }

        @media print {
          @page {
            size: letter;
            margin: 1in;
          }
        }
      </style>`;

    default:
      return baseCSS + `
        .page-container {
          width: 210mm;
          min-height: 297mm;
        }

        @media screen {
          body {
            background: #f5f5f5;
            padding: 20px;
          }
          .page-container {
            margin: 20px auto;
            padding: 20mm;
          }
        }

        @media print {
          @page {
            size: A4;
            margin: 20mm;
          }
        }
      </style>`;
  }
}

/**
 * GET /api/templates/[id]/view - Serve template HTML file for viewing
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Construct file path - templates are stored in public/templates/{template_name}/
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);
    const filePath = join(templateDir, template.filename);

    // Check if file exists
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read HTML file
    let htmlContent = await readFile(filePath, 'utf-8');

    // Rewrite image paths to use our API endpoint
    // Handle the {template_name}_files/ pattern (e.g., Good_moral_certificate_files/)
    const escapedTemplateName = template.template_name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const templateFilesPattern = new RegExp(
      `src\\s*=\\s*["'](?:\\.\\/)?${escapedTemplateName}_files\\/([^"']+)["']`,
      'gi'
    );
    htmlContent = htmlContent.replace(
      templateFilesPattern,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Handle URL-encoded template names (e.g., MEDICAL%20CERTIFICATE_files/)
    const urlEncodedTemplateName = encodeURIComponent(template.template_name);
    const escapedUrlEncodedTemplateName = urlEncodedTemplateName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const urlEncodedTemplateFilesPattern = new RegExp(
      `src\\s*=\\s*["'](?:\\.\\/)?${escapedUrlEncodedTemplateName}_files\\/([^"']+)["']`,
      'gi'
    );
    htmlContent = htmlContent.replace(
      urlEncodedTemplateFilesPattern,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Replace relative image paths with API endpoints
    htmlContent = htmlContent.replace(
      /src\s*=\s*["'](?:\.\/)?images\/([^"']+)["']/gi,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Also handle images without the images/ folder prefix
    htmlContent = htmlContent.replace(
      /src\s*=\s*["'](?!http|\/api)([^"']+\.(jpg|jpeg|png|gif|svg|webp))["']/gi,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Apply layout size styling
    const layoutCSS = getLayoutCSS(template.layout_size || 'A4');

    // Inject CSS and wrap content in page container
    if (htmlContent.includes('</head>')) {
      // Add CSS to existing head
      htmlContent = htmlContent.replace('</head>', `${layoutCSS}</head>`);

      // Wrap body content in page container
      if (htmlContent.includes('<body')) {
        htmlContent = htmlContent.replace(/<body[^>]*>/, (match) =>
          `${match}<div class="page-container">`
        );
        htmlContent = htmlContent.replace('</body>', '</div></body>');
      }
    } else if (htmlContent.includes('<html')) {
      // If no head tag, inject after html tag
      htmlContent = htmlContent.replace(/<html[^>]*>/, (match) =>
        `${match}<head>${layoutCSS}</head><body><div class="page-container">`
      );
      htmlContent = htmlContent.replace('</html>', '</div></body></html>');
    } else {
      // If no html structure, wrap the content
      htmlContent = `<!DOCTYPE html><html><head>${layoutCSS}</head><body><div class="page-container">${htmlContent}</div></body></html>`;
    }

    // Return HTML content with proper headers
    return new NextResponse(htmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Error serving template file:', error);
    return NextResponse.json(
      { error: 'Failed to serve template file' },
      { status: 500 }
    );
  }
}
