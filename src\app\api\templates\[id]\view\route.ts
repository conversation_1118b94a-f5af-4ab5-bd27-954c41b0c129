import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById } from '@/lib/database';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * Generate CSS for different layout sizes
 */
function getLayoutCSS(layoutSize: string): string {
  const baseCSS = `
    <style>
      /* Outer container for page sizing - doesn't interfere with original content */
      .ldis-page-wrapper {
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        
        padding: 1in;
        box-sizing: border-box;
      }

      /* Inner container preserves original HTML positioning */
      .ldis-content-container {
        /* Don't use position: relative to avoid creating stacking context */
        /* This allows negative z-index elements to work properly */
        width: 100%;
        height: 100%;
        overflow: visible;
      }

      /* Screen-only styling */
      @media screen {
        body {
          background: #f5f5f5;
          margin: 0;
          padding: 20px;
        }
      }

      /* Print styling */
      @media print {
        body {
          margin: 0;
          padding: 0;
          background: white;
        }
        .ldis-page-wrapper {
          box-shadow: none;
          margin: 0;
          padding: 1in;
        }
        @page {
          margin: 0;
          size: auto;
        }
      }
    `;

  switch (layoutSize.toUpperCase()) {
    case 'A4':
      return baseCSS + `
        .ldis-page-wrapper {
          width: 210mm;
          min-height: 297mm;
        }

        @media print {
          .ldis-page-wrapper {
            width: 210mm;
            min-height: 297mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>`;

    case 'LETTER':
      return baseCSS + `
        .ldis-page-wrapper {
          width: 8.5in;
          min-height: 11in;
        }

        @media print {
          .ldis-page-wrapper {
            width: 8.5in;
            min-height: 11in;
          }
          @page {
            size: letter;
            margin: 0;
          }
        }
      </style>`;

    default:
      return baseCSS + `
        .ldis-page-wrapper {
          width: 210mm;
          min-height: 297mm;
        }

        @media print {
          .ldis-page-wrapper {
            width: 210mm;
            min-height: 297mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>`;
  }
}

/**
 * GET /api/templates/[id]/view - Serve template HTML file for viewing
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Construct file path - templates are stored in public/templates/{template_name}/
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);
    const filePath = join(templateDir, template.filename);

    // Check if file exists
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read HTML file
    let htmlContent = await readFile(filePath, 'utf-8');

    // Rewrite image paths to use our API endpoint
    // Handle the {template_name}_files/ pattern (e.g., Good_moral_certificate_files/)
    const escapedTemplateName = template.template_name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const templateFilesPattern = new RegExp(
      `src\\s*=\\s*["'](?:\\.\\/)?${escapedTemplateName}_files\\/([^"']+)["']`,
      'gi'
    );
    htmlContent = htmlContent.replace(
      templateFilesPattern,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Handle URL-encoded template names (e.g., MEDICAL%20CERTIFICATE_files/)
    const urlEncodedTemplateName = encodeURIComponent(template.template_name);
    const escapedUrlEncodedTemplateName = urlEncodedTemplateName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const urlEncodedTemplateFilesPattern = new RegExp(
      `src\\s*=\\s*["'](?:\\.\\/)?${escapedUrlEncodedTemplateName}_files\\/([^"']+)["']`,
      'gi'
    );
    htmlContent = htmlContent.replace(
      urlEncodedTemplateFilesPattern,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Replace relative image paths with API endpoints
    htmlContent = htmlContent.replace(
      /src\s*=\s*["'](?:\.\/)?images\/([^"']+)["']/gi,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Also handle images without the images/ folder prefix
    htmlContent = htmlContent.replace(
      /src\s*=\s*["'](?!http|\/api)([^"']+\.(jpg|jpeg|png|gif|svg|webp))["']/gi,
      `src="/api/templates/${templateId}/images/$1"`
    );

    // Apply layout size styling
    const layoutCSS = getLayoutCSS(template.layout_size || 'A4');

    // Inject CSS and wrap content in non-interfering containers
    if (htmlContent.includes('</head>')) {
      // Add CSS to existing head
      htmlContent = htmlContent.replace('</head>', `${layoutCSS}</head>`);

      // Wrap body content in layout containers that don't interfere with original positioning
      if (htmlContent.includes('<body')) {
        htmlContent = htmlContent.replace(/<body[^>]*>/, (match) =>
          `${match}<div class="ldis-page-wrapper"><div class="ldis-content-container">`
        );
        htmlContent = htmlContent.replace('</body>', '</div></div></body>');
      }
    } else if (htmlContent.includes('<html')) {
      // If no head tag, inject after html tag
      htmlContent = htmlContent.replace(/<html[^>]*>/, (match) =>
        `${match}<head>${layoutCSS}</head><body><div class="ldis-page-wrapper"><div class="ldis-content-container">`
      );
      htmlContent = htmlContent.replace('</html>', '</div></div></body></html>');
    } else {
      // If no html structure, wrap the content
      htmlContent = `<!DOCTYPE html><html><head>${layoutCSS}</head><body><div class="ldis-page-wrapper"><div class="ldis-content-container">${htmlContent}</div></div></body></html>`;
    }

    // Return HTML content with proper headers
    return new NextResponse(htmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Error serving template file:', error);
    return NextResponse.json(
      { error: 'Failed to serve template file' },
      { status: 500 }
    );
  }
}
