import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById } from '@/lib/database';
import { readFile, writeFile, access } from 'fs/promises';
import { join } from 'path';

/**
 * GET /api/templates/[id]/preview - Get or generate template preview image
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Build paths
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);
    const previewPath = join(templateDir, 'preview.png');

    try {
      // Check if preview image already exists
      await access(previewPath);
      
      // Read and return existing preview
      const imageBuffer = await readFile(previewPath);
      
      return new NextResponse(imageBuffer, {
        headers: {
          'Content-Type': 'image/png',
          'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        },
      });
      
    } catch (error) {
      // Preview doesn't exist, return instruction to generate client-side
      return NextResponse.json({
        needsGeneration: true,
        templateUrl: `/api/templates/${templateId}/view`
      });
    }

  } catch (error) {
    console.error('Error serving template preview:', error);
    return NextResponse.json(
      { error: 'Failed to serve template preview' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/templates/[id]/preview - Save generated preview image
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Get image data from request body
    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    
    if (!imageFile) {
      return NextResponse.json(
        { error: 'No image provided' },
        { status: 400 }
      );
    }

    // Convert to buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Build paths
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);
    const previewPath = join(templateDir, 'preview.png');

    // Save the image
    await writeFile(previewPath, buffer);

    return NextResponse.json({
      success: true,
      previewUrl: `/api/templates/${templateId}/preview`
    });

  } catch (error) {
    console.error('Error saving template preview:', error);
    return NextResponse.json(
      { error: 'Failed to save template preview' },
      { status: 500 }
    );
  }
}
