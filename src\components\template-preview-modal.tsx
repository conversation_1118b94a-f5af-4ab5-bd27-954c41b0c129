"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import html2canvas from "html2canvas";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username: string;
}

interface TemplatePreviewModalProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplatePreviewModal({
  template,
  open,
  onOpenChange,
}: TemplatePreviewModalProps) {
  const [screenshotUrl, setScreenshotUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  // Load or generate screenshot when modal opens
  useEffect(() => {
    if (open && template) {
      loadOrGenerateScreenshot();
    } else {
      // Reset state when modal closes
      setScreenshotUrl("");
      setError("");
      setLoading(false);
    }
  }, [open, template]);

  const loadOrGenerateScreenshot = async () => {
    if (!template) return;

    setLoading(true);
    setError("");

    try {
      // First, try to get existing preview
      const previewResponse = await fetch(
        `/api/templates/${template.id}/preview`
      );

      if (previewResponse.ok) {
        // Preview exists, use it directly
        const blob = await previewResponse.blob();
        const url = URL.createObjectURL(blob);
        setScreenshotUrl(url);
        setLoading(false);
        return;
      }

      // Check if we need to generate
      const previewData = await previewResponse.json();
      if (!previewData.needsGeneration) {
        throw new Error("Unexpected response from preview API");
      }

      // Generate new screenshot
      await generateAndSaveScreenshot();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load preview";
      setError(errorMessage);
      setLoading(false);
    }
  };

  const generateAndSaveScreenshot = async () => {
    if (!template) return;

    try {
      // Create a hidden iframe to load the template
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "1200px";
      iframe.style.height = "1600px";
      iframe.style.border = "none";

      document.body.appendChild(iframe);

      // Load template HTML in iframe
      const templateUrl = `/api/templates/${template.id}/view`;
      iframe.src = templateUrl;

      // Wait for iframe to load
      await new Promise((resolve, reject) => {
        iframe.onload = resolve;
        iframe.onerror = reject;
        setTimeout(reject, 10000); // 10 second timeout
      });

      // Wait a bit more for content to render
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Capture screenshot using html2canvas
      const canvas = await html2canvas(iframe.contentDocument!.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
      });

      // Convert to blob and save to server
      canvas.toBlob(async (blob) => {
        if (blob) {
          // Save to server
          const formData = new FormData();
          formData.append("image", blob, "preview.png");

          await fetch(`/api/templates/${template.id}/preview`, {
            method: "POST",
            body: formData,
          });

          // Use the blob for immediate display
          const url = URL.createObjectURL(blob);
          setScreenshotUrl(url);
        } else {
          throw new Error("Failed to generate screenshot");
        }
      }, "image/png");

      // Clean up iframe
      document.body.removeChild(iframe);
      setLoading(false);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to generate preview";
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Clean up blob URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
    }
    onOpenChange(false);
  };

  const handleApply = () => {
    // TODO: Implement apply for certificate functionality
    console.log("Apply for certificate:", template?.template_name);
    // Clean up blob URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col p-0">
        {/* Hidden title for accessibility */}
        <VisuallyHidden>
          <DialogTitle>
            {template?.template_name || "Template Preview"}
          </DialogTitle>
        </VisuallyHidden>

        {/* Template Preview Image */}
        <div className="flex-1 min-h-0 overflow-auto p-6">
          {template && (
            <>
              {/* Loading skeleton while generating screenshot */}
              {loading && (
                <div className="space-y-4">
                  <Skeleton className="h-[600px] w-full rounded-lg" />
                  <div className="text-center text-sm text-muted-foreground">
                    Generating preview...
                  </div>
                </div>
              )}

              {/* Error state */}
              {error && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Template screenshot */}
              {screenshotUrl && !loading && !error && (
                <div className="flex justify-center">
                  <img
                    src={screenshotUrl}
                    alt={`Preview of ${template.template_name}`}
                    className="max-w-full h-auto rounded-lg shadow-lg border"
                  />
                </div>
              )}
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex justify-end gap-3 p-6 border-t bg-muted/20">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={loading || !!error}>
            Apply for Certificate
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
