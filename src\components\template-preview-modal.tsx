"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  AlertCircle, 
  X, 
  Calendar,
  User,
  Tag
} from "lucide-react";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username: string;
}

interface TemplatePreviewModalProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplatePreviewModal({
  template,
  open,
  onOpenChange,
}: TemplatePreviewModalProps) {
  const [previewHtml, setPreviewHtml] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  // Fetch template preview when modal opens
  useEffect(() => {
    if (open && template) {
      fetchTemplatePreview();
    } else {
      // Reset state when modal closes
      setPreviewHtml("");
      setError("");
    }
  }, [open, template]);

  const fetchTemplatePreview = async () => {
    if (!template) return;

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/templates/${template.id}/view`);
      
      if (!response.ok) {
        throw new Error("Failed to load template preview");
      }

      const html = await response.text();
      setPreviewHtml(html);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load preview";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleApply = () => {
    // TODO: Implement apply for certificate functionality
    console.log("Apply for certificate:", template?.template_name);
    // For now, just close the modal
    onOpenChange(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {template?.template_name || "Template Preview"}
          </DialogTitle>
          <DialogDescription>
            Preview the document template before applying for certificate
          </DialogDescription>
        </DialogHeader>

        {template && (
          <div className="flex-shrink-0 space-y-4 border-b pb-4">
            {/* Template Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Uploaded by:</span>
                <span className="font-medium">{template.username}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Date:</span>
                <span className="font-medium">{formatDate(template.uploaded_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Size:</span>
                <Badge variant="outline" className="text-xs">
                  {template.layout_size || "A4"}
                </Badge>
              </div>
            </div>

            {/* Description */}
            {template.description && (
              <div>
                <p className="text-sm text-muted-foreground">{template.description}</p>
              </div>
            )}

            {/* Placeholders */}
            {template.placeholders.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Required Information:</p>
                <div className="flex flex-wrap gap-1">
                  {template.placeholders.map((placeholder, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {placeholder}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Preview Content */}
        <div className="flex-1 min-h-0 overflow-auto">
          {loading && (
            <div className="space-y-4 p-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-32 w-full" />
            </div>
          )}

          {error && (
            <Alert className="m-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {previewHtml && !loading && !error && (
            <div className="p-4">
              <div 
                className="border rounded-lg bg-white shadow-sm overflow-auto"
                style={{ minHeight: "400px" }}
                dangerouslySetInnerHTML={{ __html: previewHtml }}
              />
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={loading || !!error}>
            Apply for Certificate
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
