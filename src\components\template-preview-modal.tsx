"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import html2canvas from "html2canvas";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username: string;
}

interface TemplatePreviewModalProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplatePreviewModal({
  template,
  open,
  onOpenChange,
}: TemplatePreviewModalProps) {
  const [screenshotUrl, setScreenshotUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  // Load or generate screenshot when modal opens
  useEffect(() => {
    if (open && template) {
      loadOrGenerateScreenshot();
    } else {
      // Reset state when modal closes
      setScreenshotUrl("");
      setError("");
      setLoading(false);
    }
  }, [open, template]);

  const loadOrGenerateScreenshot = async () => {
    if (!template) return;

    setLoading(true);
    setError("");

    try {
      // First, try to get existing preview
      const previewResponse = await fetch(
        `/api/templates/${template.id}/preview`
      );

      if (previewResponse.ok) {
        // Preview exists, use it directly
        const blob = await previewResponse.blob();
        const url = URL.createObjectURL(blob);
        setScreenshotUrl(url);
        setLoading(false);
        return;
      }

      // If preview doesn't exist (404 or other error), generate new screenshot
      console.log("No existing preview found, generating new one...");
      await generateAndSaveScreenshot();
    } catch (err) {
      console.error("Error in loadOrGenerateScreenshot:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load preview";
      setError(errorMessage);
      setLoading(false);
    }
  };

  const generateAndSaveScreenshot = async () => {
    if (!template) return;

    console.log(
      "Starting screenshot generation for template:",
      template.template_name
    );

    try {
      // Create a hidden iframe to load the template
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "1200px";
      iframe.style.height = "1600px";
      iframe.style.border = "none";

      document.body.appendChild(iframe);

      // Load template HTML in iframe
      const templateUrl = `/api/templates/${template.id}/view`;
      console.log("Loading template URL:", templateUrl);
      iframe.src = templateUrl;

      // Wait for iframe to load
      await new Promise((resolve, reject) => {
        iframe.onload = () => {
          console.log("Iframe loaded successfully");
          resolve(undefined);
        };
        iframe.onerror = (error) => {
          console.error("Iframe load error:", error);
          reject(error);
        };
        setTimeout(() => {
          console.error("Iframe load timeout");
          reject(new Error("Iframe load timeout"));
        }, 10000); // 10 second timeout
      });

      // Wait a bit more for content to render
      console.log("Waiting for content to render...");
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Check if iframe content is accessible
      if (!iframe.contentDocument || !iframe.contentDocument.body) {
        throw new Error("Cannot access iframe content");
      }

      console.log("Capturing screenshot with html2canvas...");

      // Capture screenshot using html2canvas
      const canvas = await html2canvas(iframe.contentDocument.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
      });

      console.log("Screenshot captured, converting to blob...");

      // Convert to blob and save to server
      canvas.toBlob(async (blob) => {
        try {
          if (blob) {
            console.log("Blob created, saving to server...");

            // Save to server
            const formData = new FormData();
            formData.append("image", blob, "preview.png");

            const saveResponse = await fetch(
              `/api/templates/${template.id}/preview`,
              {
                method: "POST",
                body: formData,
              }
            );

            if (!saveResponse.ok) {
              console.error(
                "Failed to save preview to server:",
                await saveResponse.text()
              );
            } else {
              console.log("Preview saved to server successfully");
            }

            // Use the blob for immediate display
            const url = URL.createObjectURL(blob);
            setScreenshotUrl(url);
            setLoading(false);
          } else {
            throw new Error("Failed to generate screenshot blob");
          }
        } catch (blobError) {
          console.error("Error in blob processing:", blobError);
          setError("Failed to process screenshot");
          setLoading(false);
        }
      }, "image/png");

      // Clean up iframe
      document.body.removeChild(iframe);
    } catch (err) {
      console.error("Error in generateAndSaveScreenshot:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to generate preview";
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Clean up blob URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
    }
    onOpenChange(false);
  };

  const handleApply = () => {
    // TODO: Implement apply for certificate functionality
    console.log("Apply for certificate:", template?.template_name);
    // Clean up blob URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col p-0">
        {/* Hidden title for accessibility */}
        <VisuallyHidden>
          <DialogTitle>
            {template?.template_name || "Template Preview"}
          </DialogTitle>
        </VisuallyHidden>

        {/* Template Preview Image */}
        <div className="flex-1 min-h-0 overflow-auto p-6">
          {template && (
            <>
              {/* Loading skeleton while generating screenshot */}
              {loading && (
                <div className="space-y-4">
                  <Skeleton className="h-[600px] w-full rounded-lg" />
                  <div className="text-center text-sm text-muted-foreground">
                    Generating preview...
                  </div>
                </div>
              )}

              {/* Error state */}
              {error && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Template screenshot */}
              {screenshotUrl && !loading && !error && (
                <div className="flex justify-center">
                  <img
                    src={screenshotUrl}
                    alt={`Preview of ${template.template_name}`}
                    className="max-w-full h-auto rounded-lg shadow-lg border"
                  />
                </div>
              )}
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex justify-end gap-3 p-6 border-t bg-muted/20">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={loading || !!error}>
            Apply for Certificate
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
