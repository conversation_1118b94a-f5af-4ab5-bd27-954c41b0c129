import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById, deleteTemplate } from '@/lib/database';
import { rmdir, unlink } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * Utility function to delete preview image for a template
 */
async function deletePreviewImage(templateName: string) {
  try {
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, templateName);
    const previewPath = join(templateDir, 'preview.png');

    if (existsSync(previewPath)) {
      await unlink(previewPath);
      console.log(`Deleted preview image: ${previewPath}`);
    }
  } catch (error) {
    console.error('Error deleting preview image:', error);
    // Don't throw - this is not critical
  }
}

/**
 * DELETE /api/templates/[id] - Delete a template and its associated files
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database first to get template info
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Delete template from database
    await deleteTemplate(templateId);

    // Delete preview image first (if it exists)
    await deletePreviewImage(template.template_name);

    // Delete template files and directory
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);

    if (existsSync(templateDir)) {
      try {
        await rmdir(templateDir, { recursive: true });
        console.log(`Deleted template directory: ${templateDir}`);
      } catch (fileError) {
        console.error('Error deleting template files:', fileError);
        // Don't fail the entire operation if file deletion fails
        // The database record is already deleted
      }
    }

    return NextResponse.json(
      { 
        message: 'Template deleted successfully',
        templateId,
        templateName: template.template_name
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/templates/[id] - Get a specific template by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Parse placeholders from JSON string to array
    const templateWithParsedPlaceholders = {
      ...template,
      placeholders: template.placeholders ? JSON.parse(template.placeholders) : []
    };

    return NextResponse.json(templateWithParsedPlaceholders);

  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch template' },
      { status: 500 }
    );
  }
}
